import tailwindcss from '@tailwindcss/vite';
import oxlintPlugin from 'vite-plugin-oxlint';
export default defineNuxtConfig({
  css: ['./assets/css/tailwind.css'],
  vite: {
    plugins: [tailwindcss(), oxlintPlugin()],
  },
  modules: ['@nuxthub/core', 'nuxt-mcp', 'shadcn-nuxt'],
  shadcn: {
    prefix: '',
    componentDir: './app/components/ui',
  },
  devtools: { enabled: true },

  runtimeConfig: {
    public: {
      helloText: 'Hello from the Edge 👋',
    },
  },
  future: { compatibilityVersion: 4 },
  compatibilityDate: '2025-03-01',
  hub: {},
});
