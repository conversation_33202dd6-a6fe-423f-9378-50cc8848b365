import tailwindcss from '@tailwindcss/vite';
import oxlintPlugin from 'vite-plugin-oxlint';
export default defineNuxtConfig({
  css: ['./assets/css/tailwind.css'],
  vite: {
    plugins: [tailwindcss(), oxlintPlugin()],
  },
  modules: ['@nuxthub/core', 'nuxt-mcp', 'shadcn-nuxt', '@vueuse/motion/nuxt'],
  shadcn: {
    prefix: '',
    componentDir: './app/components/ui',
  },
  motion: {
    directives: {
      'pop-in': {
        initial: { scale: 0, opacity: 0 },
        enter: {
          scale: 1,
          opacity: 1,
          transition: { duration: 200, ease: 'linear' },
        },
      },
      'slide-left': {
        initial: { x: -50, opacity: 0 },
        enter: {
          x: 0,
          opacity: 1,
          transition: { duration: 250, ease: 'linear' },
        },
      },
      'slide-right': {
        initial: { x: 50, opacity: 0 },
        enter: {
          x: 0,
          opacity: 1,
          transition: { duration: 250, ease: 'linear' },
        },
      },
      'fade-in': {
        initial: { opacity: 0 },
        enter: { opacity: 1, transition: { duration: 300, ease: 'linear' } },
      },
      'bounce-in': {
        initial: { scale: 0.8, opacity: 0 },
        enter: {
          scale: 1,
          opacity: 1,
          transition: { duration: 200, ease: 'linear' },
        },
      },
    },
  },
  devtools: { enabled: true },

  runtimeConfig: {
    public: {
      helloText: 'Hello from the Edge 👋',
    },
  },
  future: { compatibilityVersion: 4 },
  compatibilityDate: '2025-03-01',
  hub: {},
});
