import type { HTMLAttributes } from 'vue';

import { computed } from 'vue';
import { cn } from '@/lib/utils';

interface Props {
  class?: HTMLAttributes['class'];
  animation?:
    | 'pop-in'
    | 'slide-left'
    | 'slide-right'
    | 'fade-in'
    | 'bounce-in'
    | 'none';
  delay?: number;
  motionPreset?: string;
}

const props = withDefaults(defineProps<Props>(), {
  animation: 'none',
  delay: 0,
});

const motionConfig = computed(() => {
  if (props.animation === 'none') return {};

  switch (props.animation) {
    case 'pop-in':
      return {
        initial: { scale: 0, opacity: 0 },
        enter: {
          scale: 1,
          opacity: 1,
          transition: {
            duration: 200,
            ease: 'linear',
            delay: props.delay,
          },
        },
      };
    case 'slide-left':
      return {
        initial: { x: -50, opacity: 0 },
        enter: {
          x: 0,
          opacity: 1,
          transition: {
            duration: 250,
            ease: 'linear',
            delay: props.delay,
          },
        },
      };
    case 'slide-right':
      return {
        initial: { x: 50, opacity: 0 },
        enter: {
          x: 0,
          opacity: 1,
          transition: {
            duration: 250,
            ease: 'linear',
            delay: props.delay,
          },
        },
      };
    case 'bounce-in':
      return {
        initial: { scale: 0.8, opacity: 0 },
        enter: {
          scale: 1,
          opacity: 1,
          transition: {
            duration: 200,
            ease: 'linear',
            delay: props.delay,
          },
        },
      };
    case 'fade-in':
      return {
        initial: { opacity: 0 },
        enter: {
          opacity: 1,
          transition: {
            duration: 300,
            ease: 'linear',
            delay: props.delay,
          },
        },
      };
    default:
      return {};
  }
});
</script>

<template>
  <div
    v-motion
    v-bind="motionConfig"
    data-slot="card"
    :
class="
      cn(
        'bg-card text-card-foreground flex flex-col gap-brutal-xl border-brutal shadow-brutal brutal-override py-brutal-xl',
        props.class,
      )
    "
  >
    <slot />
  </div>
</template>
