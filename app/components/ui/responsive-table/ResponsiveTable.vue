<script setup lang="ts">
import { computed } from 'vue';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface TableColumn {
  key: string;
  label: string;
  class?: string;
}

interface TableRow {
  [key: string]: any;
}

interface Props {
  columns: TableColumn[];
  data: TableRow[];
  class?: string;
  cardClass?: string;
  tableClass?: string;
  mobileBreakpoint?: 'sm' | 'md' | 'lg';
}

const props = withDefaults(defineProps<Props>(), {
  mobileBreakpoint: 'md',
});

const breakpointClass = computed(() => {
  switch (props.mobileBreakpoint) {
    case 'sm':
      return 'sm:block';
    case 'lg':
      return 'lg:block';
    default:
      return 'md:block';
  }
});

const mobileBreakpointClass = computed(() => {
  switch (props.mobileBreakpoint) {
    case 'sm':
      return 'sm:hidden';
    case 'lg':
      return 'lg:hidden';
    default:
      return 'md:hidden';
  }
});
</script>

<template>
  <div :class="cn('w-full', props.class)">
    <!-- Desktop Table -->
    <div :class="cn('hidden', breakpointClass)">
      <div :class="cn('border-brutal bg-brutal-white shadow-brutal overflow-hidden', props.tableClass)">
        <!-- Table Header -->
        <div class="border-brutal-heavy-bottom bg-brutal-charcoal">
          <div class="grid gap-brutal" :style="`grid-template-columns: repeat(${columns.length}, 1fr)`">
            <div
              v-for="column in columns"
              :key="column.key"
              class="font-brutal mobile-padding text-brutal-white"
              :class="column.class"
            >
              {{ column.label }}
            </div>
          </div>
        </div>
        
        <!-- Table Body -->
        <div class="divide-y divide-brutal-black">
          <div
            v-for="(row, index) in data"
            :key="index"
            class="grid gap-brutal hover-brutal-electric transition-all duration-200"
            :style="`grid-template-columns: repeat(${columns.length}, 1fr)`"
          >
            <div
              v-for="column in columns"
              :key="column.key"
              class="font-mono-brutal mobile-padding text-brutal-black"
              :class="column.class"
            >
              <slot :name="`cell-${column.key}`" :row="row" :value="row[column.key]">
                {{ row[column.key] }}
              </slot>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Cards -->
    <div :class="cn('space-y-brutal', mobileBreakpointClass)">
      <Card
        v-for="(row, index) in data"
        :key="index"
        :class="cn('border-brutal bg-brutal-white shadow-brutal hover-brutal-electric mobile-tap', props.cardClass)"
      >
        <CardHeader v-if="$slots['card-header']">
          <slot name="card-header" :row="row" :index="index" />
        </CardHeader>
        
        <CardContent class="space-y-brutal">
          <div
            v-for="column in columns"
            :key="column.key"
            class="flex flex-col gap-1"
          >
            <div class="font-brutal text-xs text-brutal-black uppercase tracking-wider">
              {{ column.label }}
            </div>
            <div class="font-mono-brutal mobile-text text-brutal-black" :class="column.class">
              <slot :name="`cell-${column.key}`" :row="row" :value="row[column.key]">
                {{ row[column.key] }}
              </slot>
            </div>
          </div>
          
          <div v-if="$slots['card-actions']" class="pt-brutal border-brutal-heavy-top">
            <slot name="card-actions" :row="row" :index="index" />
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
