<script setup lang="ts">
import { computed } from 'vue';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface DataItem {
  id: string | number;
  title: string;
  subtitle?: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  accent?: 'neon-lime' | 'neon-cyan' | 'electric-blue' | 'hot-magenta' | 'acid-green' | 'plasma-orange';
  icon?: string;
  metadata?: Record<string, any>;
}

interface Props {
  data: DataItem[];
  class?: string;
  cardClass?: string;
  columns?: 1 | 2 | 3 | 4;
  showChange?: boolean;
  showSubtitle?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  columns: 2,
  showChange: true,
  showSubtitle: true,
});

const gridClass = computed(() => {
  const baseClass = 'mobile-grid gap-brutal';
  switch (props.columns) {
    case 1:
      return `${baseClass} grid-cols-1`;
    case 3:
      return `${baseClass} grid-cols-1 sm:grid-cols-2 lg:grid-cols-3`;
    case 4:
      return `${baseClass} grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4`;
    default:
      return `${baseClass} grid-cols-1 sm:grid-cols-2`;
  }
});

const getAccentClasses = (accent?: string) => {
  switch (accent) {
    case 'neon-lime':
      return 'shadow-brutal-neon-lime hover-brutal-neon';
    case 'neon-cyan':
      return 'shadow-brutal-neon-cyan hover-brutal-cyan';
    case 'electric-blue':
      return 'shadow-brutal-electric-blue hover-brutal-electric';
    case 'hot-magenta':
      return 'shadow-brutal-hot-magenta hover-brutal-magenta';
    case 'acid-green':
      return 'shadow-brutal-acid-green hover-brutal-neon';
    case 'plasma-orange':
      return 'shadow-brutal-plasma-orange hover-brutal-neon';
    default:
      return 'shadow-brutal hover-brutal';
  }
};

const getChangeClasses = (changeType?: string) => {
  switch (changeType) {
    case 'positive':
      return 'text-acid-green';
    case 'negative':
      return 'text-laser-red';
    default:
      return 'text-brutal-black';
  }
};
</script>

<template>
  <div :class="cn('w-full', props.class)">
    <div :class="gridClass">
      <Card
        v-for="item in data"
        :key="item.id"
        :class="cn(
          'border-brutal bg-brutal-white mobile-tap transition-all duration-200',
          getAccentClasses(item.accent),
          props.cardClass
        )"
      >
        <CardHeader class="pb-brutal">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <CardTitle class="font-brutal mobile-text text-brutal-black">
                {{ item.title }}
              </CardTitle>
              <div
                v-if="showSubtitle && item.subtitle"
                class="font-mono-brutal text-xs text-brutal-black mt-1"
              >
                {{ item.subtitle }}
              </div>
            </div>
            <div v-if="item.icon" class="ml-brutal">
              <Icon :name="item.icon" class="size-6 text-brutal-black" />
            </div>
          </div>
        </CardHeader>
        
        <CardContent class="space-y-brutal">
          <!-- Main Value -->
          <div class="font-brutal text-2xl sm:text-3xl text-brutal-black">
            {{ item.value }}
          </div>
          
          <!-- Change Indicator -->
          <div
            v-if="showChange && item.change"
            class="font-mono-brutal text-xs sm:text-sm"
            :class="getChangeClasses(item.changeType)"
          >
            {{ item.change }}
          </div>
          
          <!-- Metadata -->
          <div v-if="item.metadata" class="space-y-1 pt-brutal border-brutal-heavy-top">
            <div
              v-for="(value, key) in item.metadata"
              :key="key"
              class="flex justify-between items-center"
            >
              <span class="font-brutal text-xs text-brutal-black uppercase tracking-wider">
                {{ key }}
              </span>
              <span class="font-mono-brutal text-xs text-brutal-black">
                {{ value }}
              </span>
            </div>
          </div>
          
          <!-- Custom Slot -->
          <div v-if="$slots.default">
            <slot :item="item" />
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
