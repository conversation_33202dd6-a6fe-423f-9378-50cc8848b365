<script setup lang="ts">
import { computed } from 'vue';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface Props {
  class?: string;
  animation?: 'pop-in' | 'slide-left' | 'slide-right' | 'fade-in' | 'bounce-in' | 'none';
  delay?: number;
  accent?: 'neon-lime' | 'neon-cyan' | 'electric-blue' | 'hot-magenta' | 'acid-green' | 'plasma-orange';
  hover?: boolean;
  clickable?: boolean;
  title?: string;
  subtitle?: string;
}

const props = withDefaults(defineProps<Props>(), {
  animation: 'pop-in',
  delay: 0,
  hover: true,
  clickable: false,
});

const emit = defineEmits<{
  click: [event: MouseEvent];
}>();

const motionConfig = computed(() => {
  if (props.animation === 'none') return {};
  
  const baseConfig = {
    initial: { opacity: 0 },
    enter: { 
      opacity: 1, 
      transition: { 
        duration: 200, 
        ease: 'linear',
        delay: props.delay 
      } 
    },
  };

  switch (props.animation) {
    case 'pop-in':
      return {
        initial: { scale: 0, opacity: 0 },
        enter: { 
          scale: 1, 
          opacity: 1, 
          transition: { 
            duration: 200, 
            ease: 'linear',
            delay: props.delay 
          } 
        },
      };
    case 'slide-left':
      return {
        initial: { x: -50, opacity: 0 },
        enter: { 
          x: 0, 
          opacity: 1, 
          transition: { 
            duration: 250, 
            ease: 'linear',
            delay: props.delay 
          } 
        },
      };
    case 'slide-right':
      return {
        initial: { x: 50, opacity: 0 },
        enter: { 
          x: 0, 
          opacity: 1, 
          transition: { 
            duration: 250, 
            ease: 'linear',
            delay: props.delay 
          } 
        },
      };
    case 'bounce-in':
      return {
        initial: { scale: 0.8, opacity: 0 },
        enter: { 
          scale: 1, 
          opacity: 1, 
          transition: { 
            duration: 200, 
            ease: 'linear',
            delay: props.delay 
          } 
        },
      };
    default:
      return baseConfig;
  }
});

const accentClasses = computed(() => {
  switch (props.accent) {
    case 'neon-lime':
      return 'shadow-brutal-neon-lime hover-brutal-neon';
    case 'neon-cyan':
      return 'shadow-brutal-neon-cyan hover-brutal-cyan';
    case 'electric-blue':
      return 'shadow-brutal-electric-blue hover-brutal-electric';
    case 'hot-magenta':
      return 'shadow-brutal-hot-magenta hover-brutal-magenta';
    case 'acid-green':
      return 'shadow-brutal-acid-green hover-brutal-neon';
    case 'plasma-orange':
      return 'shadow-brutal-plasma-orange hover-brutal-neon';
    default:
      return 'shadow-brutal hover-brutal';
  }
});

const cardClasses = computed(() => {
  return cn(
    'border-brutal bg-brutal-white transition-all duration-200 brutal-override',
    accentClasses.value,
    {
      'mobile-tap': props.clickable,
      'cursor-pointer': props.clickable,
    },
    props.class
  );
});

const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event);
  }
};
</script>

<template>
  <Card
    v-motion
    v-bind="motionConfig"
    :class="cardClasses"
    @click="handleClick"
  >
    <CardHeader v-if="title || subtitle || $slots.header">
      <slot name="header">
        <CardTitle v-if="title" class="font-brutal mobile-text text-brutal-black">
          {{ title }}
        </CardTitle>
        <div v-if="subtitle" class="font-mono-brutal text-xs text-brutal-black mt-1">
          {{ subtitle }}
        </div>
      </slot>
    </CardHeader>
    
    <CardContent>
      <slot />
    </CardContent>
    
    <div v-if="$slots.footer" class="px-brutal-xl pb-brutal-xl">
      <slot name="footer" />
    </div>
  </Card>
</template>
