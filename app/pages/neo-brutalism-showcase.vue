<script setup lang="ts">
import { ref } from 'vue';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AnimatedCard } from '@/components/ui/animated-card';
import { ResponsiveTable } from '@/components/ui/responsive-table';
import { MobileDataGrid } from '@/components/ui/mobile-data-grid';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// SEO and meta
useHead({
  title: 'Neo-Brutalism Showcase - DEFI.AI',
  meta: [
    {
      name: 'description',
      content: 'Complete Neo-Brutalism design system showcase with animations, mobile responsiveness, and brutal styling.',
    },
  ],
});

// Sample data for components
const portfolioData = [
  { id: 1, title: 'TOTAL VALUE', subtitle: 'Portfolio Balance', value: '$2.4M', change: '+12.3%', changeType: 'positive', accent: 'electric-blue' },
  { id: 2, title: 'APY', subtitle: 'Annual Yield', value: '24.7%', change: '****%', changeType: 'positive', accent: 'acid-green' },
  { id: 3, title: 'RISK SCORE', subtitle: 'Portfolio Risk', value: '6.2/10', change: 'MODERATE', changeType: 'neutral', accent: 'hot-magenta' },
  { id: 4, title: 'GAS EFFICIENCY', subtitle: 'Optimization', value: '87%', change: 'EFFICIENT', changeType: 'positive', accent: 'neon-cyan' },
];

const tableColumns = [
  { key: 'protocol', label: 'PROTOCOL' },
  { key: 'allocation', label: 'ALLOCATION' },
  { key: 'apy', label: 'APY' },
  { key: 'risk', label: 'RISK' },
  { key: 'status', label: 'STATUS' },
];

const tableData = [
  { protocol: 'AAVE V3', allocation: '$850K', apy: '12.5%', risk: 'LOW', status: 'ACTIVE' },
  { protocol: 'UNISWAP V3', allocation: '$650K', apy: '28.3%', risk: 'MEDIUM', status: 'ACTIVE' },
  { protocol: 'LIDO', allocation: '$450K', apy: '8.7%', risk: 'LOW', status: 'ACTIVE' },
  { protocol: 'COMPOUND', allocation: '$350K', apy: '15.2%', risk: 'MEDIUM', status: 'PENDING' },
];

const animationTypes = ['pop-in', 'slide-left', 'slide-right', 'fade-in', 'bounce-in'];
const currentAnimation = ref(0);

const cycleAnimation = () => {
  currentAnimation.value = (currentAnimation.value + 1) % animationTypes.length;
};
</script>

<template>
  <div class="min-h-screen bg-brutal-white">
    <!-- Header -->
    <div class="border-brutal-heavy-bottom bg-brutal-black mobile-padding py-6 sm:py-8">
      <div class="mx-auto max-w-7xl">
        <h1 class="font-brutal mobile-heading text-brutal-white">
          NEO-BRUTALISM
          <span class="text-electric-blue">SHOWCASE</span>
        </h1>
        <p class="font-mono-brutal mt-2 mobile-text text-brutal-white">
          > COMPLETE DESIGN SYSTEM WITH ANIMATIONS & MOBILE RESPONSIVENESS
        </p>
      </div>
    </div>

    <div class="mx-auto max-w-7xl mobile-padding py-6 sm:py-8 space-y-8 sm:space-y-12">
      
      <!-- Animation Showcase -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          @VUEUSE/MOTION ANIMATIONS
        </h2>
        <div class="space-y-6">
          <div class="flex flex-wrap gap-4 mb-6">
            <Button @click="cycleAnimation" class="border-brutal bg-neon-lime text-brutal-black hover-brutal-neon">
              CYCLE ANIMATION: {{ animationTypes[currentAnimation].toUpperCase() }}
            </Button>
            <Badge variant="secondary" class="font-brutal">
              LINEAR TIMING • 200-300MS • ACCESSIBILITY AWARE
            </Badge>
          </div>
          
          <div class="mobile-grid gap-6">
            <AnimatedCard
              :key="currentAnimation"
              :animation="animationTypes[currentAnimation]"
              accent="electric-blue"
              title="ANIMATED CARD"
              subtitle="Sharp mechanical animations"
              clickable
            >
              <div class="font-mono-brutal mobile-text text-brutal-black">
                Animation: {{ animationTypes[currentAnimation] }}
              </div>
            </AnimatedCard>
            
            <Card
              :key="`card-${currentAnimation}`"
              :animation="animationTypes[currentAnimation]"
              :delay="100"
              class="border-brutal bg-brutal-black shadow-brutal-acid-green hover-brutal-neon mobile-tap"
            >
              <CardHeader>
                <CardTitle class="font-brutal mobile-text text-acid-green">ENHANCED CARD</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="font-mono-brutal mobile-text text-brutal-white">
                  Built-in animation support with accessibility considerations
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <!-- Mobile Data Grid -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          MOBILE-FIRST DATA GRID
        </h2>
        <MobileDataGrid 
          :data="portfolioData" 
          :columns="2"
          :show-change="true"
          :show-subtitle="true"
        />
      </section>

      <!-- Responsive Table -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          RESPONSIVE TABLE → CARDS
        </h2>
        <p class="font-mono-brutal mobile-text text-brutal-black mb-6">
          > TRANSFORMS TO CARDS BELOW 768PX BREAKPOINT
        </p>
        <ResponsiveTable 
          :columns="tableColumns" 
          :data="tableData"
          mobile-breakpoint="md"
        >
          <template #cell-status="{ value }">
            <Badge 
              :variant="value === 'ACTIVE' ? 'default' : 'secondary'"
              class="font-brutal"
            >
              {{ value }}
            </Badge>
          </template>
        </ResponsiveTable>
      </section>

      <!-- Color System -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          COMPLETE COLOR SYSTEM
        </h2>
        <div class="space-y-6">
          <div class="mobile-grid gap-4">
            <div class="border-neon-lime border-brutal bg-neon-lime mobile-padding text-center">
              <div class="font-brutal mobile-text text-brutal-black">NEON LIME</div>
            </div>
            <div class="border-electric-blue border-brutal bg-electric-blue mobile-padding text-center">
              <div class="font-brutal mobile-text text-brutal-white">ELECTRIC BLUE</div>
            </div>
            <div class="border-hot-magenta border-brutal bg-hot-magenta mobile-padding text-center">
              <div class="font-brutal mobile-text text-brutal-white">HOT MAGENTA</div>
            </div>
            <div class="border-acid-green border-brutal bg-acid-green mobile-padding text-center">
              <div class="font-brutal mobile-text text-brutal-black">ACID GREEN</div>
            </div>
          </div>
          
          <div class="border-brutal bg-brutal-charcoal mobile-padding">
            <p class="font-mono-brutal mobile-text text-brutal-white mb-4">
              Complete Tailwind utilities: text-, bg-, border- variants for all neon colors
            </p>
            <div class="flex flex-wrap gap-2">
              <Badge class="bg-neon-cyan text-brutal-black border-neon-cyan">NEON CYAN</Badge>
              <Badge class="bg-plasma-orange text-brutal-black border-plasma-orange">PLASMA ORANGE</Badge>
              <Badge class="bg-laser-red text-brutal-white border-laser-red">LASER RED</Badge>
              <Badge class="bg-toxic-yellow text-brutal-black border-toxic-yellow">TOXIC YELLOW</Badge>
            </div>
          </div>
        </div>
      </section>

      <!-- Border Radius Fixes -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          BRUTAL-OVERRIDE CONSISTENCY
        </h2>
        <p class="font-mono-brutal mobile-text text-brutal-black mb-6">
          > ALL SHADCN-NUXT COMPONENTS NOW ENFORCE BORDER-RADIUS: 0
        </p>
        <div class="space-y-4 border-brutal bg-brutal-charcoal mobile-padding">
          <div class="text-brutal-white font-mono-brutal text-sm space-y-2">
            <div>✅ DropdownMenuContent.vue - brutal-override applied</div>
            <div>✅ TooltipContent.vue - brutal-override applied</div>
            <div>✅ SelectContent.vue - brutal-override applied</div>
            <div>✅ Input.vue - brutal-override applied</div>
            <div>✅ TabsList.vue & TabsTrigger.vue - brutal-override applied</div>
            <div>✅ Progress.vue - brutal-override applied</div>
          </div>
        </div>
      </section>

    </div>
  </div>
</template>
